import { NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase/admin'

export async function POST() {
  console.log('🧪 Registration Language Test: Starting test...');

  try {
    // Check if this is a development environment
    if (process.env.NODE_ENV !== 'development') {
      console.log('🧪 Registration Language Test: Only available in development');
      return NextResponse.json(
        { error: 'Test endpoint only available in development environment' },
        { status: 403 }
      );
    }

    // Test email for registration
    const testEmail = `test-${Date.now()}@example.com`;
    const testPassword = 'TestPassword123!';
    const testFirstName = 'Test';
    const testLastName = 'User';
    const testLanguage = 'fr'; // French for testing

    console.log('🧪 Registration Language Test: Creating test user with language:', testLanguage);

    // Create user with admin client (simulating registration with preferred language)
    const { data: authData, error: authError } = await supabaseAdmin.auth.admin.createUser({
      email: testEmail,
      password: testPassword,
      email_confirm: true,
      user_metadata: {
        first_name: testFirstName,
        last_name: testLastName,
        preferred_language: testLanguage
      }
    });

    if (authError) {
      console.error('🧪 Registration Language Test: Auth error:', authError);
      return NextResponse.json(
        { error: 'Failed to create test user', details: authError.message },
        { status: 500 }
      );
    }

    console.log('🧪 Registration Language Test: Auth user created:', authData.user.id);

    // Wait a moment for the trigger to execute
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Check if user was created in users table with correct language
    const { data: userData, error: userError } = await supabaseAdmin
      .from('users')
      .select('id, email, first_name, last_name, preferred_language')
      .eq('id', authData.user.id)
      .single();

    if (userError) {
      console.error('🧪 Registration Language Test: User data error:', userError);
      // Clean up auth user
      await supabaseAdmin.auth.admin.deleteUser(authData.user.id);
      return NextResponse.json(
        { error: 'Failed to fetch user data', details: userError.message },
        { status: 500 }
      );
    }

    console.log('🧪 Registration Language Test: User data retrieved:', userData);

    // Clean up test user
    await supabaseAdmin.auth.admin.deleteUser(authData.user.id);
    console.log('🧪 Registration Language Test: Test user cleaned up');

    // Verify results
    const success = userData.preferred_language === testLanguage;
    
    return NextResponse.json({
      success,
      message: success 
        ? 'Registration language test passed successfully' 
        : 'Registration language test failed',
      results: {
        expectedLanguage: testLanguage,
        actualLanguage: userData.preferred_language,
        userCreated: !!userData,
        languageMatches: success
      },
      testDetails: {
        testEmail,
        testLanguage,
        userMetadata: authData.user.user_metadata,
        databaseRecord: userData
      }
    });

  } catch (error) {
    console.error('🧪 Registration Language Test: Unexpected error:', error);
    return NextResponse.json(
      { 
        error: 'Test failed with unexpected error',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
