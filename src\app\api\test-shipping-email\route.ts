import { NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase/admin'
import { sendShippingNotificationEmail } from '@/lib/email'

export async function POST() {
  console.log('🧪 API: Test shipping email endpoint called');

  try {
    // Check if this is a development environment
    if (process.env.NODE_ENV !== 'development') {
      console.log('🧪 API: Test shipping email only available in development');
      return NextResponse.json(
        { error: 'Test shipping email only available in development environment' },
        { status: 403 }
      );
    }

    // Get the most recent confirmed order from the database
    console.log('🧪 API: Fetching recent confirmed order...');
    const { data: recentOrder, error: orderError } = await supabaseAdmin
      .from('orders')
      .select('*')
      .eq('status', 'confirmed')
      .eq('payment_status', 'paid')
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    let testOrder;
    
    if (orderError || !recentOrder) {
      console.log('🧪 API: No recent order found, creating mock order...');
      // Create a mock order for testing
      testOrder = {
        id: 'test-order-id',
        order_number: 'TEST-001',
        email: process.env.NEXT_PUBLIC_COMPANY_EMAIL || '<EMAIL>',
        status: 'shipped',
        subtotal: 45.00,
        shipping_cost: 5.90,
        tax_amount: 3.93,
        discount_amount: 0,
        total_amount: 54.83,
        currency: 'CHF',
        payment_status: 'paid',
        shipping_address: {
          firstName: 'Max',
          lastName: 'Mustermann',
          street: 'Musterstrasse 123',
          postalCode: '8001',
          city: 'Zürich',
          country: 'Schweiz'
        },
        billing_address: {
          firstName: 'Max',
          lastName: 'Mustermann',
          street: 'Musterstrasse 123',
          postalCode: '8001',
          city: 'Zürich',
          country: 'Schweiz'
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
    } else {
      testOrder = recentOrder;
      console.log('🧪 API: Using real order:', testOrder.id);
    }

    const testTrackingNumber = 'TEST123456789';
    interface EmailResult {
      success: boolean;
      messageId?: string;
      response?: string;
      error?: string;
    }

    const results: {
      errors: string[];
      deEmail?: EmailResult;
      itEmail?: EmailResult;
      frEmail?: EmailResult;
    } = {
      errors: []
    };

    // Test all three languages
    const locales = ['de', 'it', 'fr'];
    
    for (const locale of locales) {
      try {
        console.log(`🧪 API: Testing shipping email in ${locale}...`);
        const result = await sendShippingNotificationEmail(testOrder, testTrackingNumber, locale);
        const emailResult: EmailResult = {
          success: true,
          messageId: result.messageId,
          response: result.response
        };

        if (locale === 'de') results.deEmail = emailResult;
        else if (locale === 'it') results.itEmail = emailResult;
        else if (locale === 'fr') results.frEmail = emailResult;
        console.log(`🧪 API: ${locale.toUpperCase()} shipping email sent successfully:`, result.messageId);
      } catch (error) {
        console.error(`🧪 API: Error sending ${locale} shipping email:`, error);
        const emailResult: EmailResult = {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };

        if (locale === 'de') results.deEmail = emailResult;
        else if (locale === 'it') results.itEmail = emailResult;
        else if (locale === 'fr') results.frEmail = emailResult;

        results.errors.push(`${locale.toUpperCase()} email failed: ` + (error instanceof Error ? error.message : String(error)));
      }
    }

    const overallSuccess = results.deEmail?.success && results.itEmail?.success && results.frEmail?.success;

    console.log('🧪 API: Test shipping email completed:', {
      success: overallSuccess,
      deEmailSuccess: results.deEmail?.success,
      itEmailSuccess: results.itEmail?.success,
      frEmailSuccess: results.frEmail?.success,
      errorCount: results.errors.length
    });

    return NextResponse.json({
      success: overallSuccess,
      message: overallSuccess 
        ? 'All test shipping emails sent successfully' 
        : 'Some test shipping emails failed',
      results,
      testOrder: {
        id: testOrder.id,
        email: testOrder.email,
        totalAmount: testOrder.total_amount,
        trackingNumber: testTrackingNumber
      }
    }, { status: overallSuccess ? 200 : 500 });

  } catch (error) {
    console.error('🧪 API: Error in test shipping email endpoint:', error);
    return NextResponse.json({
      success: false,
      message: 'Test shipping email endpoint failed',
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
