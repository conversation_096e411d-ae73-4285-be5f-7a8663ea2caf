import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { awardPointsForLinkedOrders } from '@/lib/gamification'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { linkedOrderIds } = await request.json()

    if (!linkedOrderIds || !Array.isArray(linkedOrderIds)) {
      return NextResponse.json(
        { error: 'Invalid linkedOrderIds' },
        { status: 400 }
      )
    }

    // Award points retroactively for linked orders
    const { totalPointsAwarded, ordersProcessed } = await awardPointsForLinkedOrders(
      user.id,
      linkedOrderIds
    )

    return NextResponse.json({
      success: true,
      totalPointsAwarded,
      ordersProcessed
    })
  } catch (error) {
    console.error('Error awarding retroactive points:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
