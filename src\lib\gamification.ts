import { createClient } from '@/lib/supabase/server'
import { createClient as createAdminClient } from '@supabase/supabase-js'

// Create admin client for bypassing RLS when needed
const supabaseAdmin = createAdminClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export interface UserLevel {
  id: string
  level: number
  name: string
  minimum_points: number
  discount_percentage: number
  points_multiplier: number
}

export interface GiftReward {
  id: string
  type: 'level' | 'league'
  trigger_value: number
  option_1_product_id: string | null
  option_2_product_id: string | null
  option_3_product_id: string | null
  is_active: boolean
}

export interface UserGiftClaim {
  id: string
  user_id: string
  gift_reward_id: string
  selected_product_id: string | null
  claimed_at: string
  order_id: string | null
  notes: string | null
  status: 'pending' | 'selected' | 'fulfilled'
}

export interface LeagueConfig {
  id: string
  levels_per_league: number
  league_1_points: number
  league_2_points: number
  league_3_points: number
  league_4_points: number
  league_5_points: number
  league_1_discount: number
  league_2_discount: number
  league_3_discount: number
  league_4_discount: number
  league_5_discount: number
  global_multiplier: number
  multiplier_description: string
  is_active: boolean
}

// Default gamification settings
const DEFAULT_POINTS_PER_CHF = parseFloat(process.env.POINTS_PER_CHF || '1')

/**
 * Get points per CHF from admin settings
 */
export async function getPointsPerCHF(): Promise<number> {
  try {
    const { data: settings } = await supabaseAdmin
      .from('site_settings')
      .select('points_per_chf')
      .maybeSingle()

    const pointsPerCHF = settings?.points_per_chf || DEFAULT_POINTS_PER_CHF
    console.log(`🎮 Gamification: Points per CHF: ${pointsPerCHF}`)
    return pointsPerCHF
  } catch (error) {
    console.error('Error fetching points per CHF setting:', error)
    return DEFAULT_POINTS_PER_CHF
  }
}

/**
 * Get league configuration
 */
export async function getLeagueConfig(): Promise<LeagueConfig | null> {
  const { data: config } = await supabaseAdmin
    .from('league_config')
    .select('*')
    .eq('is_active', true)
    .single()

  return config || null
}

/**
 * Generate levels automatically based on league configuration using SQL function
 */
export async function generateLevelsFromConfig(): Promise<void> {
  try {
    const { error } = await supabaseAdmin.rpc('generate_levels_from_config')

    if (error) {
      console.error('Error generating levels:', error)
      throw error
    } else {
      console.log('✅ Levels generated automatically using SQL function')
    }
  } catch (error) {
    console.error('Error generating levels from config:', error)
    throw error
  }
}

/**
 * Calculate league number from level (every 10 levels = 1 league)
 */
export function calculateLeague(level: number): number {
  return Math.ceil(level / 10)
}

/**
 * Get league discount percentage based on league number
 */
export async function getLeagueDiscount(league: number): Promise<number> {
  const config = await getLeagueConfig()
  if (!config) return 0

  switch (league) {
    case 1: return config.league_1_discount
    case 2: return config.league_2_discount
    case 3: return config.league_3_discount
    case 4: return config.league_4_discount
    case 5: return config.league_5_discount
    default: return config.league_5_discount // Max discount for higher leagues
  }
}

/**
 * Calculate points earned for a purchase amount (simplified - always uses global multiplier)
 */
export async function calculatePointsEarned(amount: number): Promise<number> {
  const pointsPerCHF = await getPointsPerCHF()
  const config = await getLeagueConfig()
  const globalMultiplier = config?.global_multiplier || 1.0

  const basePoints = amount * pointsPerCHF
  return Math.floor(basePoints * globalMultiplier)
}

/**
 * Get user's current level based on total points
 */
export async function getUserLevel(totalPoints: number): Promise<UserLevel | null> {
  // Use admin client to ensure we can read user levels
  const { data: levels } = await supabaseAdmin
    .from('user_levels')
    .select('*')
    .lte('minimum_points', totalPoints)
    .order('level', { ascending: false })
    .limit(1)

  return levels?.[0] || null
}

/**
 * Get all user levels
 */
export async function getAllUserLevels(): Promise<UserLevel[]> {
  const supabase = await createClient()
  
  const { data: levels } = await supabase
    .from('user_levels')
    .select('*')
    .order('level')

  return levels || []
}

/**
 * Sync all users' current_level based on their total_points
 * This is a utility function to fix any inconsistencies
 */
export async function syncAllUserLevels(): Promise<void> {
  console.log('🔄 Gamification: Starting user level synchronization...')

  try {
    // Get all users with points
    const { data: users, error: usersError } = await supabaseAdmin
      .from('users')
      .select('id, total_points, current_level')
      .gt('total_points', 0)

    if (usersError) {
      console.error('🔄 Gamification: Error fetching users:', usersError)
      return
    }

    if (!users || users.length === 0) {
      console.log('🔄 Gamification: No users with points found')
      return
    }

    let updatedCount = 0

    for (const user of users) {
      const correctLevel = await getUserLevel(user.total_points || 0)
      const correctLevelNumber = correctLevel?.level || 1

      if (user.current_level !== correctLevelNumber) {
        const { error: updateError } = await supabaseAdmin
          .from('users')
          .update({ current_level: correctLevelNumber })
          .eq('id', user.id)

        if (updateError) {
          console.error(`🔄 Gamification: Error updating user ${user.id}:`, updateError)
        } else {
          console.log(`🔄 Gamification: Updated user ${user.id} from level ${user.current_level} to ${correctLevelNumber}`)
          updatedCount++
        }
      }
    }

    console.log(`🔄 Gamification: Synchronization complete. Updated ${updatedCount} users.`)
  } catch (error) {
    console.error('🔄 Gamification: Error during synchronization:', error)
  }
}

/**
 * Update user's level and points after a purchase
 */
export async function updateUserGamification(
  userId: string,
  purchaseAmount: number,
  orderId?: string
): Promise<{ newLevel: UserLevel | null; pointsEarned: number }> {
  console.log(`🎮 Gamification: Starting update for user ${userId}, purchase amount: ${purchaseAmount}`)

  // Use admin client to ensure we can read and update user data
  const { data: user, error: userError } = await supabaseAdmin
    .from('users')
    .select('lifetime_spend, current_level, total_points')
    .eq('id', userId)
    .single()

  if (userError || !user) {
    console.error('🎮 Gamification: Error fetching user data:', userError)
    return { newLevel: null, pointsEarned: 0 }
  }

  console.log(`🎮 Gamification: Current user data:`, {
    lifetime_spend: user.lifetime_spend,
    current_level: user.current_level,
    total_points: user.total_points
  })

  const newLifetimeSpend = (user.lifetime_spend || 0) + purchaseAmount

  // Get current level info based on current points
  const currentLevel = await getUserLevel(user.total_points || 0)
  console.log(`🎮 Gamification: Current level:`, currentLevel)

  // Calculate points earned for this purchase (simplified - no level multiplier)
  const pointsEarned = await calculatePointsEarned(purchaseAmount)
  const newTotalPoints = (user.total_points || 0) + pointsEarned

  console.log(`🎮 Gamification: Points calculation:`, {
    purchaseAmount,
    globalMultiplier: (await getLeagueConfig())?.global_multiplier || 1.0,
    pointsEarned,
    newTotalPoints
  })

  // Determine new level based on new total points
  const newLevel = await getUserLevel(newTotalPoints)
  console.log(`🎮 Gamification: New level:`, newLevel)

  // Update user using admin client
  const { error: updateError } = await supabaseAdmin
    .from('users')
    .update({
      lifetime_spend: newLifetimeSpend,
      current_level: newLevel?.level || 1,
      total_points: newTotalPoints
    })
    .eq('id', userId)

  if (updateError) {
    console.error('🎮 Gamification: Error updating user:', updateError)
    return { newLevel: null, pointsEarned: 0 }
  }

  // Check and assign gifts based on level progression
  try {
    await checkAndAssignGifts(userId, newLevel, currentLevel, orderId)
  } catch (giftError) {
    console.error('🎁 Gift System: Error checking/assigning gifts:', giftError)
    // Don't fail the gamification update for gift errors
  }

  console.log(`🎮 Gamification: Successfully updated user ${userId} with ${pointsEarned} points`)
  return { newLevel, pointsEarned }
}

/**
 * Get available gift rewards for a specific level
 */
export async function getGiftRewardForLevel(level: number): Promise<GiftReward | null> {
  const supabase = await createClient()

  const { data: reward } = await supabase
    .from('gift_rewards')
    .select('*')
    .eq('type', 'level')
    .eq('trigger_value', level)
    .eq('is_active', true)
    .single()

  return reward || null
}

/**
 * Get available gift rewards for a specific league
 */
export async function getGiftRewardForLeague(league: number): Promise<GiftReward | null> {
  const supabase = await createClient()

  const { data: reward } = await supabase
    .from('gift_rewards')
    .select('*')
    .eq('type', 'league')
    .eq('trigger_value', league)
    .eq('is_active', true)
    .single()

  return reward || null
}

/**
 * Get user's pending gift claims
 */
export async function getUserPendingGifts(userId: string): Promise<UserGiftClaim[]> {
  const { data: claims } = await supabaseAdmin
    .from('user_gift_claims')
    .select('*')
    .eq('user_id', userId)
    .eq('status', 'pending')
    .order('claimed_at', { ascending: false })

  return claims || []
}

/**
 * Check and assign gifts based on level progression
 */
export async function checkAndAssignGifts(
  userId: string,
  newLevel: UserLevel | null,
  oldLevel: UserLevel | null,
  orderId?: string
): Promise<void> {
  if (!newLevel || !oldLevel) {
    console.log('🎁 Gift System: Missing level information, skipping gift assignment')
    return
  }

  const newLevelNum = newLevel.level
  const oldLevelNum = oldLevel.level

  console.log(`🎁 Gift System: Checking gifts for user ${userId}, old level: ${oldLevelNum}, new level: ${newLevelNum}`)

  if (newLevelNum <= oldLevelNum) {
    console.log('🎁 Gift System: No level progression, no gifts to assign')
    return
  }

  // Calculate leagues
  const newLeague = calculateLeague(newLevelNum)
  const oldLeague = calculateLeague(oldLevelNum)
  const leagueProgressed = newLeague > oldLeague

  console.log(`🎁 Gift System: League progression - old: ${oldLeague}, new: ${newLeague}, progressed: ${leagueProgressed}`)

  // Priority: League gift takes precedence over level gift
  if (leagueProgressed) {
    // Assign league gift for the new league reached
    await assignLeagueGift(userId, newLeague, orderId)
  } else {
    // Assign level gift for the new level reached
    await assignLevelGift(userId, newLevelNum, orderId)
  }
}

/**
 * Assign a level gift to user
 */
async function assignLevelGift(userId: string, level: number, orderId?: string): Promise<void> {
  console.log(`🎁 Gift System: Assigning level gift for level ${level}`)

  const reward = await getGiftRewardForLevel(level)
  if (!reward) {
    console.log(`🎁 Gift System: No level reward configured for level ${level}`)
    return
  }

  await createGiftClaim(userId, reward, orderId, `Level ${level} reward`)
}

/**
 * Assign a league gift to user
 */
async function assignLeagueGift(userId: string, league: number, orderId?: string): Promise<void> {
  console.log(`🎁 Gift System: Assigning league gift for league ${league}`)

  const reward = await getGiftRewardForLeague(league)
  if (!reward) {
    console.log(`🎁 Gift System: No league reward configured for league ${league}`)
    return
  }

  await createGiftClaim(userId, reward, orderId, `League ${league} reward`)
}

/**
 * Create a gift claim for user
 */
async function createGiftClaim(
  userId: string,
  reward: GiftReward,
  orderId?: string,
  notes?: string
): Promise<void> {
  try {
    // Check if user already has this gift claim
    const { data: existingClaim } = await supabaseAdmin
      .from('user_gift_claims')
      .select('id')
      .eq('user_id', userId)
      .eq('gift_reward_id', reward.id)
      .single()

    if (existingClaim) {
      console.log(`🎁 Gift System: User ${userId} already has claim for reward ${reward.id}`)
      return
    }

    const { error } = await supabaseAdmin
      .from('user_gift_claims')
      .insert({
        user_id: userId,
        gift_reward_id: reward.id,
        order_id: orderId,
        notes: notes || `${reward.type} ${reward.trigger_value} reward`,
        status: 'pending'
      })

    if (error) {
      console.error(`🎁 Gift System: Error creating gift claim:`, error)
    } else {
      console.log(`🎁 Gift System: Successfully created ${reward.type} gift claim for user ${userId}`)
    }
  } catch (error) {
    console.error(`🎁 Gift System: Exception creating gift claim:`, error)
  }
}

/**
 * Calculate discount for user level (now league-based)
 */
export function calculateLevelDiscount(amount: number, level: UserLevel | null): number {
  if (!level || !level.discount_percentage) return 0
  return amount * (level.discount_percentage / 100)
}

/**
 * Calculate league-based discount for user
 */
export async function calculateLeagueDiscount(amount: number, userLevel: number): Promise<number> {
  const league = calculateLeague(userLevel)
  const discountPercentage = await getLeagueDiscount(league)
  return amount * (discountPercentage / 100)
}

/**
 * Get user's discount percentage (league-based)
 */
export async function getUserDiscountPercentage(userId: string): Promise<number> {
  const supabase = await createClient()

  const { data: user } = await supabase
    .from('users')
    .select('total_points, current_level')
    .eq('id', userId)
    .single()

  if (!user) return 0

  const level = user.current_level || 1
  const league = calculateLeague(level)
  return await getLeagueDiscount(league)
}

/**
 * Initialize default user levels if none exist
 */
export async function initializeDefaultLevels(): Promise<void> {
  const supabase = await createClient()
  
  // Check if levels already exist
  const { count } = await supabase
    .from('user_levels')
    .select('*', { count: 'exact', head: true })

  if (count && count > 0) return

  // Create default levels
  const defaultLevels = [
    {
      level: 1,
      name: 'Bronze',
      minimum_points: 0,
      discount_percentage: 0,
      points_multiplier: 1.0
    },
    {
      level: 2,
      name: 'Silver',
      minimum_points: 200,
      discount_percentage: 5,
      points_multiplier: 1.2
    },
    {
      level: 3,
      name: 'Gold',
      minimum_points: 500,
      discount_percentage: 10,
      points_multiplier: 1.5
    },
    {
      level: 4,
      name: 'Platinum',
      minimum_points: 1000,
      discount_percentage: 15,
      points_multiplier: 2.0
    }
  ]

  await supabase
    .from('user_levels')
    .insert(defaultLevels)
}

/**
 * Get user's gift history with product details
 */
export async function getUserGiftHistory(userId: string): Promise<UserGiftClaim[]> {
  const { data: claims } = await supabaseAdmin
    .from('user_gift_claims')
    .select(`
      *,
      gift_rewards!inner(type, trigger_value),
      products(id, title, images, price)
    `)
    .eq('user_id', userId)
    .order('claimed_at', { ascending: false })

  return claims || []
}

/**
 * Get user's gifts ready for order (selected but not yet added to an order)
 */
export async function getUserGiftsReadyForOrder(userId: string): Promise<UserGiftClaim[]> {
  const { data: claims } = await supabaseAdmin
    .from('user_gift_claims')
    .select(`
      *,
      gift_rewards!inner(type, trigger_value)
    `)
    .eq('user_id', userId)
    .eq('status', 'ready_for_order')
    .order('claimed_at', { ascending: false })

  // Manually fetch product details for selected products
  if (claims && claims.length > 0) {
    const productIds = claims
      .map(claim => claim.selected_product_id)
      .filter(Boolean)

    if (productIds.length > 0) {
      const { data: products } = await supabaseAdmin
        .from('products')
        .select('id, title, images, price')
        .in('id', productIds)

      // Attach product data to claims
      claims.forEach(claim => {
        if (claim.selected_product_id) {
          const product = products?.find(p => p.id === claim.selected_product_id)
          if (product) {
            (claim as UserGiftClaim & { products?: { id: string; title: string; images: string[]; price: number } }).products = product
          }
        }
      })
    }
  }

  return claims || []
}

/**
 * Mark gifts as added to order
 */
export async function markGiftsAsAddedToOrder(
  userId: string,
  claimIds: string[],
  orderId: string
): Promise<boolean> {
  try {
    const { error } = await supabaseAdmin
      .from('user_gift_claims')
      .update({
        status: 'added_to_order',
        order_id: orderId
      })
      .eq('user_id', userId)
      .in('id', claimIds)
      .eq('status', 'ready_for_order')

    if (error) {
      console.error('🎁 Gift System: Error marking gifts as added to order:', error)
      return false
    }

    console.log(`🎁 Gift System: Marked ${claimIds.length} gifts as added to order ${orderId}`)
    return true
  } catch (error) {
    console.error('🎁 Gift System: Exception marking gifts as added to order:', error)
    return false
  }
}

/**
 * Award points retroactively for linked guest orders
 */
export async function awardPointsForLinkedOrders(
  userId: string,
  linkedOrderIds: string[]
): Promise<{ totalPointsAwarded: number; ordersProcessed: number }> {
  console.log(`🎮 Gamification: Awarding points retroactively for ${linkedOrderIds.length} linked orders`)

  let totalPointsAwarded = 0
  let ordersProcessed = 0

  try {
    // Get the linked orders that are paid and don't have points awarded yet
    const { data: orders, error: ordersError } = await supabaseAdmin
      .from('orders')
      .select('id, total_amount, payment_status')
      .in('id', linkedOrderIds)
      .eq('user_id', userId)
      .eq('payment_status', 'paid')

    if (ordersError) {
      console.error('🎮 Gamification: Error fetching linked orders:', ordersError)
      return { totalPointsAwarded: 0, ordersProcessed: 0 }
    }

    if (!orders || orders.length === 0) {
      console.log('🎮 Gamification: No paid orders found to award points for')
      return { totalPointsAwarded: 0, ordersProcessed: 0 }
    }

    // Get current user data
    const { data: user, error: userError } = await supabaseAdmin
      .from('users')
      .select('total_points, lifetime_spend, current_level')
      .eq('id', userId)
      .single()

    if (userError || !user) {
      console.error('🎮 Gamification: Error fetching user data:', userError)
      return { totalPointsAwarded: 0, ordersProcessed: 0 }
    }

    // Calculate total points to award
    for (const order of orders) {
      const pointsForOrder = await calculatePointsEarned(order.total_amount)
      totalPointsAwarded += pointsForOrder
      ordersProcessed++
      console.log(`🎮 Gamification: Order ${order.id}: ${order.total_amount} CHF = ${pointsForOrder} points`)
    }

    // Update user's total points and lifetime spend
    const newTotalPoints = (user.total_points || 0) + totalPointsAwarded
    const totalOrderAmount = orders.reduce((sum, order) => sum + order.total_amount, 0)
    const newLifetimeSpend = (user.lifetime_spend || 0) + totalOrderAmount

    // Determine new level based on new total points
    const newLevel = await getUserLevel(newTotalPoints)

    // Update user
    const { error: updateError } = await supabaseAdmin
      .from('users')
      .update({
        total_points: newTotalPoints,
        lifetime_spend: newLifetimeSpend,
        current_level: newLevel?.level || 1
      })
      .eq('id', userId)

    if (updateError) {
      console.error('🎮 Gamification: Error updating user with retroactive points:', updateError)
      return { totalPointsAwarded: 0, ordersProcessed: 0 }
    }

    console.log(`🎮 Gamification: Successfully awarded ${totalPointsAwarded} points for ${ordersProcessed} linked orders`)
    console.log(`🎮 Gamification: User ${userId} new totals: ${newTotalPoints} points, level ${newLevel?.level || 1}`)

    return { totalPointsAwarded, ordersProcessed }
  } catch (error) {
    console.error('🎮 Gamification: Exception in awardPointsForLinkedOrders:', error)
    return { totalPointsAwarded: 0, ordersProcessed: 0 }
  }
}

/**
 * Select a product for a pending gift claim
 */
export async function selectGiftProduct(
  userId: string,
  claimId: string,
  productId: string
): Promise<boolean> {
  try {
    const { error } = await supabaseAdmin
      .from('user_gift_claims')
      .update({
        selected_product_id: productId,
        status: 'ready_for_order'
      })
      .eq('id', claimId)
      .eq('user_id', userId)
      .eq('status', 'pending')

    if (error) {
      console.error('🎁 Gift System: Error selecting gift product:', error)
      return false
    }

    console.log(`🎁 Gift System: User ${userId} selected product ${productId} for claim ${claimId}`)
    return true
  } catch (error) {
    console.error('🎁 Gift System: Exception selecting gift product:', error)
    return false
  }
}
