-- Migration to update gift system for better checkout integration
-- This adds support for gifts in orders and updates gift claim statuses

-- Update gift_claim_status enum to include new statuses
ALTER TYPE gift_claim_status ADD VALUE 'ready_for_order';
ALTER TYPE gift_claim_status ADD VALUE 'added_to_order';

-- Add is_gift column to order_items table
ALTER TABLE order_items ADD COLUMN is_gift BOOLEAN DEFAULT FALSE;

-- Add index for gift items
CREATE INDEX idx_order_items_is_gift ON order_items(is_gift) WHERE is_gift = true;

-- Add gift_claim_id to order_items to track which gift claim generated the item
ALTER TABLE order_items ADD COLUMN gift_claim_id UUID REFERENCES user_gift_claims(id) ON DELETE SET NULL;

-- Add index for gift claim tracking
CREATE INDEX idx_order_items_gift_claim_id ON order_items(gift_claim_id) WHERE gift_claim_id IS NOT NULL;

-- Update RLS policies for order_items to handle gift items
-- Users can view their own order items (including gifts)
DROP POLICY IF EXISTS "Users can view own order items" ON order_items;
CREATE POLICY "Users can view own order items" ON order_items
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM orders 
            WHERE orders.id = order_items.order_id 
            AND orders.user_id = auth.uid()
        )
    );

-- Admins can view all order items
DROP POLICY IF EXISTS "Admins can view all order items" ON order_items;
CREATE POLICY "Admins can view all order items" ON order_items
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.is_admin = true
        )
    );

-- Update user_gift_claims policies to handle new statuses
DROP POLICY IF EXISTS "Users can update own pending claims" ON user_gift_claims;
CREATE POLICY "Users can update own pending claims" ON user_gift_claims
    FOR UPDATE USING (
        auth.uid() = user_id 
        AND status IN ('pending', 'ready_for_order')
    );

-- Add comment explaining the new gift flow
COMMENT ON COLUMN order_items.is_gift IS 'Indicates if this order item is a gift from the gamification system';
COMMENT ON COLUMN order_items.gift_claim_id IS 'Links to the user_gift_claims record that generated this gift item';
COMMENT ON TYPE gift_claim_status IS 'Gift claim status: pending -> ready_for_order -> added_to_order -> fulfilled';
