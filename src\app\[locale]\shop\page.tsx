import { createClient } from '@/lib/supabase/server'
import { ShopContent } from '@/components/shop/shop-content'

export const dynamic = 'force-dynamic';

interface ShopPageProps {
  params: Promise<{ locale: string }>;
}

export default async function ShopPage({ params }: ShopPageProps) {
  const { locale } = await params;
  const supabase = await createClient()

  // Get initial 20 products for SSR and filter options
  const { data: products, error } = await supabase
    .from('products')
    .select('*')
    .eq('is_available', true)
    .order('created_at', { ascending: false })
    .limit(20)

  if (error) {
    console.error('Error fetching products:', error)
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        <ShopContent
          initialProducts={products || []}
          locale={locale}
        />
      </div>
    </div>
  )
}
