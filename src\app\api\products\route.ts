import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const category = searchParams.get('category')
    const coffeeType = searchParams.get('coffee_type')
    const brand = searchParams.get('brand')
    const blend = searchParams.get('blend')
    const compatibility = searchParams.get('compatibility')
    const coffeeOnly = searchParams.get('coffee_only') === 'true'

    const supabase = await createClient()
    
    // Calculate offset
    const offset = (page - 1) * limit

    // Build query
    let query = supabase
      .from('products')
      .select('*', { count: 'exact' })
      .eq('is_available', true)

    // Apply filters
    if (category && category !== 'all') {
      query = query.eq('category', category)
    }

    if (coffeeOnly) {
      query = query.eq('category', 'coffee')
    }

    if (coffeeType && coffeeType !== 'all') {
      query = query.eq('coffee_type', coffeeType)
    }

    if (brand && brand !== 'all') {
      query = query.eq('brand', brand)
    }

    if (blend && blend !== 'all') {
      query = query.eq('blend', blend)
    }

    if (compatibility && compatibility !== 'all') {
      query = query.contains('machine_compatibility', [compatibility])
    }

    // Apply pagination and ordering
    const { data: products, error, count } = await query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (error) {
      console.error('Error fetching products:', error)
      return NextResponse.json(
        { error: 'Failed to fetch products' },
        { status: 500 }
      )
    }

    const totalPages = Math.ceil((count || 0) / limit)
    const hasMore = page < totalPages

    return NextResponse.json({
      products: products || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages,
        hasMore
      }
    })

  } catch (error) {
    console.error('Error in products API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
