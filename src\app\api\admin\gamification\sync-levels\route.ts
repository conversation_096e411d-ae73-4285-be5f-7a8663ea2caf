import { NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { syncAllUserLevels } from '@/lib/gamification'

// POST - Sync all user levels (admin only)
export async function POST() {
  try {
    const supabase = await createClient()

    // Check if user is authenticated and is admin
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { data: profile } = await supabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (!profile?.is_admin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Sync all user levels
    await syncAllUserLevels()

    return NextResponse.json({ 
      success: true, 
      message: 'User levels synchronized successfully' 
    })
  } catch (error) {
    console.error('Error in sync-levels POST:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
