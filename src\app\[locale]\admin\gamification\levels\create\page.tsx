'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useTranslations, useLocale } from 'next-intl'
import { createClient } from '@/lib/supabase/client'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'
import Link from 'next/link'
import LevelForm from '@/components/admin/level-form'

export default function CreateLevelPage() {
  const locale = useLocale()
  const router = useRouter()
  const t = useTranslations('admin')
  const supabase = createClient()

  const [loading, setLoading] = useState(true)
  const [authChecked, setAuthChecked] = useState(false)

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser()
        if (!user) {
          router.push(`/${locale}/login`)
          return
        }
        const { data: profile } = await supabase
          .from('users')
          .select('is_admin')
          .eq('id', user.id)
          .single()
        if (!profile?.is_admin) {
          router.push(`/${locale}`)
          return
        }
        setAuthChecked(true)
        setLoading(false)
      } catch {
        router.push(`/${locale}`)
      }
    }
    if (!authChecked) {
      checkAuth()
    }
  }, [authChecked, locale, router, supabase])

  if (loading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex justify-center items-center min-h-[400px]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex items-center gap-4 mb-8">
        <Button variant="outline" size="sm" asChild>
          <Link href={`/${locale}/admin/gamification`}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('common.back')}
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold">{t('gamificationPage.newLevel')}</h1>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{t('gamificationPage.newLevel')}</CardTitle>
        </CardHeader>
        <CardContent>
          <LevelForm locale={locale} />
        </CardContent>
      </Card>
    </div>
  )
}

