'use client'

import { useState } from 'react'
import Image from 'next/image'
import { Badge } from '@/components/ui/badge'
import { Coffee } from 'lucide-react'

interface ProductImageGalleryProps {
  images: string[]
  title: string
  hasDiscount?: boolean
  discountPercentage?: number
}

export function ProductImageGallery({ 
  images, 
  title, 
  hasDiscount = false, 
  discountPercentage = 0 
}: ProductImageGalleryProps) {
  const [selectedImage, setSelectedImage] = useState(0)

  if (!images || images.length === 0) {
    return (
      <div className="w-full aspect-square relative rounded-lg overflow-hidden">
        <div className="w-full h-full bg-gradient-card flex items-center justify-center">
          <Coffee className="h-20 w-20 text-muted-foreground" />
        </div>
        {hasDiscount && (
          <Badge className="absolute top-2 right-2 bg-gradient-to-r from-red-500 to-red-600 text-white shadow-medium">
            -{discountPercentage}%
          </Badge>
        )}
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Main Image */}
      <div className="w-full aspect-square relative rounded-lg overflow-hidden">
        <Image 
          src={images[selectedImage]} 
          alt={title} 
          fill 
          className="object-cover" 
        />
        {hasDiscount && (
          <Badge className="absolute top-2 right-2 bg-gradient-to-r from-red-500 to-red-600 text-white shadow-medium">
            -{discountPercentage}%
          </Badge>
        )}
      </div>

      {/* Thumbnail Gallery */}
      {images.length > 1 && (
        <div className="grid grid-cols-4 gap-2">
          {images.map((image, index) => (
            <button
              key={index}
              onClick={() => setSelectedImage(index)}
              className={`aspect-square rounded-lg overflow-hidden border-2 transition-all ${
                selectedImage === index 
                  ? 'border-primary shadow-md' 
                  : 'border-transparent hover:border-muted-foreground/50'
              }`}
            >
              <Image
                src={image}
                alt={`${title} ${index + 1}`}
                width={100}
                height={100}
                className="w-full h-full object-cover"
              />
            </button>
          ))}
        </div>
      )}
    </div>
  )
}
