import { NextRequest, NextResponse } from 'next/server'
import { stripe } from '@/lib/stripe'
import { createClient } from '@/lib/supabase/server'
import { createClient as createAdminClient } from '@supabase/supabase-js'
import { updateUserGamification } from '@/lib/gamification'
import { sendOrderConfirmationEmail, sendAdminOrderNotificationEmail } from '@/lib/email'

// Create admin client for bypassing RLS
const supabaseAdmin = createAdminClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function POST(request: NextRequest) {
  try {
    // Check for required environment variables at runtime
    if (!process.env.STRIPE_SECRET_KEY) {
      console.error('STRIPE_SECRET_KEY is not set')
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      )
    }

    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      console.error('Supabase environment variables are not set')
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      )
    }

    const { paymentIntentId } = await request.json()

    if (!paymentIntentId) {
      return NextResponse.json(
        { error: 'Payment Intent ID is required' },
        { status: 400 }
      )
    }

    // Retrieve the payment intent from Stripe
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId)

    const supabase = await createClient()

    // Update order based on payment status
    if (paymentIntent.status === 'succeeded') {
      // First check if order is already confirmed to prevent duplicate processing
      const { data: existingOrder, error: checkError } = await supabaseAdmin
        .from('orders')
        .select('id, status, payment_status, user_id, total_amount')
        .eq('payment_intent_id', paymentIntentId)
        .single()

      if (checkError) {
        console.error('Error checking existing order:', checkError)
        return NextResponse.json(
          { error: 'Order not found' },
          { status: 404 }
        )
      }

      // If order is already confirmed, return success without duplicate processing
      if (existingOrder.status === 'confirmed' && existingOrder.payment_status === 'paid') {
        console.log('Order already confirmed, skipping duplicate processing')
        return NextResponse.json({
          success: true,
          orderId: existingOrder.id,
          status: 'succeeded'
        })
      }

      // Use admin client to bypass RLS for guest orders
      const { data: order, error: orderError } = await supabaseAdmin
        .from('orders')
        .update({
          status: 'confirmed',
          payment_status: 'paid'
        })
        .eq('payment_intent_id', paymentIntentId)
        .select(`
          *,
          order_items (
            *,
            products (
              title
            )
          )
        `)
        .single()

      if (orderError) {
        console.error('Error updating order:', orderError)
        return NextResponse.json(
          { error: 'Failed to update order' },
          { status: 500 }
        )
      }

      // Clear the cart if metadata contains cartId
      if (paymentIntent.metadata.cartId) {
        await supabase
          .from('cart_items')
          .delete()
          .eq('cart_id', paymentIntent.metadata.cartId)

        await supabase
          .from('carts')
          .update({ status: 'completed' })
          .eq('id', paymentIntent.metadata.cartId)
      }

      // Award points to user if they are logged in
      if (order.user_id) {
        try {
          const { pointsEarned, newLevel } = await updateUserGamification(
            order.user_id,
            order.total_amount,
            order.id
          )
          console.log(`Points awarded: ${pointsEarned} points to user ${order.user_id}`)
          if (newLevel) {
            console.log(`User ${order.user_id} reached new level: ${newLevel.name}`)
          }
        } catch (pointsError) {
          console.error('Error awarding points:', pointsError)
          // Don't fail the payment confirmation for points errors
        }
      }

      // Send confirmation emails as fallback (in case webhook fails)
      console.log('💳 Confirm Payment: Starting email notifications for order:', order.id);
      try {
        // Get user's preferred language
        let userLocale = 'de'; // Default fallback
        if (order.user_id) {
          try {
            const { data: userData } = await supabaseAdmin
              .from('users')
              .select('preferred_language')
              .eq('id', order.user_id)
              .single();

            if (userData?.preferred_language) {
              userLocale = userData.preferred_language;
            }
          } catch {
            console.log('💳 Confirm Payment: Could not fetch user language, using default:', userLocale);
          }
        }

        console.log('💳 Confirm Payment: Sending customer confirmation email with locale:', userLocale);
        await sendOrderConfirmationEmail(order, userLocale);
        console.log('💳 Confirm Payment: Customer confirmation email sent successfully');

        console.log('💳 Confirm Payment: Sending admin notification email...');
        await sendAdminOrderNotificationEmail(order);
        console.log('💳 Confirm Payment: Admin notification email sent successfully');

        console.log('💳 Confirm Payment: All email notifications sent successfully for order:', order.id);
      } catch (emailError) {
        console.error('💳 Confirm Payment: Error sending emails for order:', order.id, emailError);
        console.error('💳 Confirm Payment: Email error details:', {
          name: emailError instanceof Error ? emailError.name : 'Unknown',
          message: emailError instanceof Error ? emailError.message : String(emailError),
          stack: emailError instanceof Error ? emailError.stack : undefined,
          orderId: order.id,
          customerEmail: order.email
        });
        // Don't fail the payment confirmation for email errors
        // Webhook should still handle emails as backup
      }

      return NextResponse.json({
        success: true,
        orderId: order.id,
        status: 'succeeded'
      })
    } else if (paymentIntent.status === 'requires_action') {
      return NextResponse.json({
        success: false,
        requiresAction: true,
        client_secret: paymentIntent.client_secret,
        status: paymentIntent.status
      })
    } else {
      // Payment failed - use admin client to bypass RLS
      await supabaseAdmin
        .from('orders')
        .update({
          status: 'cancelled',
          payment_status: 'failed'
        })
        .eq('payment_intent_id', paymentIntentId)

      return NextResponse.json({
        success: false,
        error: 'Payment failed',
        status: paymentIntent.status
      }, { status: 400 })
    }

  } catch (error) {
    console.error('Error confirming payment:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
