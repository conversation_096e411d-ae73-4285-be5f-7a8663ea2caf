import type { Metada<PERSON> } from "next";
import Script from "next/script";
import "./globals.css";

export const metadata: Metadata = {
  title: "PrimeCaffe - Premium Kaffee aus der Schweiz",
  description: "Entdecken Sie erstklassigen Kaffeegenuss mit PrimeCaffe. Premium Kaffeekapseln, Bohnen und Zubehör direkt aus der Schweiz.",
  keywords: "Kaffee, Kaffeekapseln, Kaffeebohnen, Schweiz, Premium, Espresso",
  authors: [{ name: "PrimeCaffe" }],
  creator: "PrimeCaffe",
  publisher: "PrimeCaffe",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const gtmId = process.env.NEXT_PUBLIC_GTM_ID;

  return (
    <html lang="de-CH">
      <head>
        {/* Google Tag Manager */}
        {gtmId && (
          <Script
            id="gtm-head"
            strategy="afterInteractive"
            dangerouslySetInnerHTML={{
              __html: `
                (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
                j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
                'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
                })(window,document,'script','dataLayer','${gtmId}');
              `
            }}
          />
        )}
      </head>
      <body
        className="antialiased min-h-screen flex flex-col overflow-x-hidden"
      >
        {/* Google Tag Manager (noscript) */}
        {gtmId && (
          <noscript>
            <iframe
              src={`https://www.googletagmanager.com/ns.html?id=${gtmId}`}
              height="0"
              width="0"
              style={{ display: 'none', visibility: 'hidden' }}
            />
          </noscript>
        )}
        {children}
      </body>
    </html>
  );
}
