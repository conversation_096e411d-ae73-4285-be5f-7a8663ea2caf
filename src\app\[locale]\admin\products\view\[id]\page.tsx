import { createClient } from '@/lib/supabase/server'
import { notFound } from 'next/navigation'
import { formatCurrency } from '@/lib/utils'
import { getTranslations } from 'next-intl/server'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Edit } from 'lucide-react'
import Link from 'next/link'
import { ProductImageGallery } from '@/components/shop/product-image-gallery'

export const dynamic = 'force-dynamic'

interface ProductPageProps {
  params: Promise<{ locale: string; id: string }>
}

export default async function AdminProductViewPage({ params }: ProductPageProps) {
  const { locale, id } = await params
  const supabase = await createClient()

  const { data: product, error } = await supabase
    .from('products')
    .select('*')
    .eq('id', id)
    .single()

  if (error || !product) {
    notFound()
  }

  const t = await getTranslations({ locale, namespace: 'admin' })
  const tc = await getTranslations({ locale, namespace: 'common' })

  const hasDiscount = product.discount_price && product.discount_price < product.price
  const displayPrice = hasDiscount ? product.discount_price : product.price

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        {/* Back & Edit */}
        <div className="flex items-center gap-2 mb-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href={`/${locale}/admin/products`}>
              <ArrowLeft className="mr-2 h-4 w-4" /> {tc('back')}
            </Link>
          </Button>
          <Button variant="outline" size="sm" asChild>
            <Link href={`/${locale}/admin/products/edit/${product.id}`}>
              <Edit className="mr-2 h-4 w-4" /> {t('products.actions.edit')}
            </Link>
          </Button>
        </div>

        <div className="grid md:grid-cols-2 gap-8 items-start">
          {/* Image Gallery */}
          <ProductImageGallery
            images={product.images || []}
            title={product.title}
            hasDiscount={hasDiscount}
            discountPercentage={hasDiscount ? Math.round(((product.price - product.discount_price) / product.price) * 100) : 0}
          />

          {/* Details */}
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold mb-2 flex items-center gap-2">
                {product.title}
                {!product.is_available && (
                  <Badge variant="secondary">{t('products.status.unavailable')}</Badge>
                )}
              </h1>
              {product.brand && <p className="text-muted-foreground mb-4">{product.brand}</p>}
              <p className="whitespace-pre-line break-words">{product.description}</p>
            </div>

            {/* Category & Attributes */}
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-muted-foreground">{t('products.categories.title')}:</span>
                <span className="capitalize">{product.category}</span>
              </div>
              {product.category === 'coffee' && (
                <>
                  {product.type && (
                    <div className="flex justify-between"><span className="text-muted-foreground">{t('shop.product.type')}:</span><span className="capitalize">{product.type}</span></div>
                  )}
                  {product.pack_quantity && (
                    <div className="flex justify-between"><span className="text-muted-foreground">{t('shop.product.pack')}:</span><span>{product.pack_quantity} {t('shop.product.pieces')}</span></div>
                  )}
                  {product.cost_per_espresso && (
                    <div className="flex justify-between"><span className="text-muted-foreground">{t('shop.product.perEspresso')}:</span><span>{formatCurrency(product.cost_per_espresso)}</span></div>
                  )}
                </>
              )}
            </div>

            {/* Price */}
            <div className="flex items-center gap-3">
              <span className="text-3xl font-bold">{formatCurrency(displayPrice)}</span>
              {hasDiscount && (
                <span className="text-muted-foreground line-through">{formatCurrency(product.price)}</span>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
