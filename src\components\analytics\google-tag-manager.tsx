'use client'

import { useEffect } from 'react'
import Script from 'next/script'

interface GoogleTagManagerProps {
  gtmId: string
}

export function GoogleTagManager({ gtmId }: GoogleTagManagerProps) {
  useEffect(() => {
    // Initialize dataLayer for GTM
    if (typeof window !== 'undefined') {
      window.dataLayer = window.dataLayer || []
      window.dataLayer.push({
        'gtm.start': new Date().getTime(),
        event: 'gtm.js'
      })
    }
  }, [gtmId])

  return (
    <>
      {/* Google Tag Manager */}
      <Script
        id="gtm-script"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
            })(window,document,'script','dataLayer','${gtmId}');
          `
        }}
      />

      {/* GTM NoScript fallback */}
      <noscript>
        <iframe
          src={`https://www.googletagmanager.com/ns.html?id=${gtmId}`}
          height="0"
          width="0"
          style={{ display: 'none', visibility: 'hidden' }}
        />
      </noscript>
    </>
  )
}

// Analytics event tracking functions using GTM dataLayer
export const trackEvent = (eventName: string, parameters?: Record<string, unknown>) => {
  if (typeof window !== 'undefined' && window.dataLayer) {
    window.dataLayer.push({
      event: eventName,
      ...parameters
    })
  }
}

export const trackPurchase = (transactionId: string, value: number, currency: string = 'CHF', items: unknown[] = []) => {
  trackEvent('purchase', {
    transaction_id: transactionId,
    value: value,
    currency: currency,
    items: items
  })
}

export const trackAddToCart = (currency: string = 'CHF', value: number, items: unknown[] = []) => {
  trackEvent('add_to_cart', {
    currency: currency,
    value: value,
    items: items
  })
}

export const trackRemoveFromCart = (currency: string = 'CHF', value: number, items: unknown[] = []) => {
  trackEvent('remove_from_cart', {
    currency: currency,
    value: value,
    items: items
  })
}

export const trackViewItem = (currency: string = 'CHF', value: number, items: unknown[] = []) => {
  trackEvent('view_item', {
    currency: currency,
    value: value,
    items: items
  })
}

export const trackBeginCheckout = (currency: string = 'CHF', value: number, items: unknown[] = []) => {
  trackEvent('begin_checkout', {
    currency: currency,
    value: value,
    items: items
  })
}

// Declare global dataLayer
declare global {
  interface Window {
    dataLayer: unknown[]
  }
}
