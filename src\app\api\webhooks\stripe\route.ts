import { NextRequest, NextResponse } from 'next/server'
import { stripe } from '@/lib/stripe'
import { supabaseAdmin } from '@/lib/supabase/admin'
import { sendOrderConfirmationEmail, sendAdminOrderNotificationEmail } from '@/lib/email'
import { updateUserGamification } from '@/lib/gamification'
import type Stripe from 'stripe'

export async function POST(request: NextRequest) {
  console.log('🔔 Webhook: Stripe webhook received at', new Date().toISOString());

  try {
    // Check for required environment variables at runtime
    const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;
    if (!webhookSecret) {
      console.error('🔔 Webhook: STRIPE_WEBHOOK_SECRET is not set')
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      )
    }

    if (!process.env.STRIPE_SECRET_KEY) {
      console.error('STRIPE_SECRET_KEY is not set')
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      )
    }

    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      console.error('Supabase environment variables are not set')
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      )
    }

    const body = await request.text()
    const signature = request.headers.get('stripe-signature')!

    console.log('🔔 Webhook: Processing webhook with signature:', signature ? 'Present' : 'Missing');

    let event: Stripe.Event
    try {
      event = stripe.webhooks.constructEvent(body, signature, webhookSecret)
      console.log('🔔 Webhook: Event constructed successfully:', {
        type: event.type,
        id: event.id,
        created: new Date(event.created * 1000).toISOString()
      });
    } catch (err) {
      console.error('🔔 Webhook: Signature verification failed:', err)
      return NextResponse.json({ error: 'Invalid signature' }, { status: 400 })
    }

    // Use admin client to bypass RLS restrictions for webhook processing
    const supabase = supabaseAdmin

    switch (event.type) {
      case 'payment_intent.succeeded': {
        console.log('🔔 Webhook: Processing payment_intent.succeeded event');
        const paymentIntent = event.data.object as Stripe.PaymentIntent
        console.log('🔔 Webhook: Payment Intent details:', {
          id: paymentIntent.id,
          amount: paymentIntent.amount,
          currency: paymentIntent.currency,
          status: paymentIntent.status,
          metadata: paymentIntent.metadata
        });

        // Update order status
        const { data: order, error: orderError } = await supabase
          .from('orders')
          .update({
            status: 'confirmed',
            payment_status: 'paid'
          })
          .eq('payment_intent_id', paymentIntent.id)
          .select(`
            *,
            order_items (
              *,
              products (
                title
              )
            )
          `)
          .single()

        if (orderError) {
          console.error('Error updating order:', orderError)
          return NextResponse.json({ error: 'Order update failed' }, { status: 500 })
        }

        if (order) {
          // Clear the cart if it exists
          const cartId = paymentIntent.metadata.cartId
          if (cartId) {
            await supabase.from('cart_items').delete().eq('cart_id', cartId)
            await supabase.from('carts').update({ status: 'completed' }).eq('id', cartId)
          }

          // Award points to user if they are logged in
          if (order.user_id) {
            try {
              const { pointsEarned, newLevel } = await updateUserGamification(
                order.user_id,
                order.total_amount,
                order.id
              )
              console.log(`Points awarded: ${pointsEarned} points to user ${order.user_id}`)
              if (newLevel) {
                console.log(`User ${order.user_id} reached new level: ${newLevel.name}`)
              }
            } catch (pointsError) {
              console.error('Error awarding points:', pointsError)
              // Don't fail the webhook for points errors
            }
          }

          // Send confirmation emails
          console.log('🔔 Webhook: Starting email notifications for order:', order.id);
          try {
            // Get user's preferred language
            let userLocale = 'de'; // Default fallback
            if (order.user_id) {
              try {
                const { data: userData } = await supabaseAdmin
                  .from('users')
                  .select('preferred_language')
                  .eq('id', order.user_id)
                  .single();

                if (userData?.preferred_language) {
                  userLocale = userData.preferred_language;
                }
              } catch {
                console.log('🔔 Webhook: Could not fetch user language, using default:', userLocale);
              }
            }

            console.log('🔔 Webhook: Sending customer confirmation email with locale:', userLocale);
            await sendOrderConfirmationEmail(order, userLocale);
            console.log('🔔 Webhook: Customer confirmation email sent successfully');

            console.log('🔔 Webhook: Sending admin notification email...');
            await sendAdminOrderNotificationEmail(order);
            console.log('🔔 Webhook: Admin notification email sent successfully');

            console.log('🔔 Webhook: All email notifications sent successfully for order:', order.id);
          } catch (emailError) {
            console.error('🔔 Webhook: Error sending emails for order:', order.id, emailError);
            console.error('🔔 Webhook: Email error details:', {
              name: emailError instanceof Error ? emailError.name : 'Unknown',
              message: emailError instanceof Error ? emailError.message : String(emailError),
              stack: emailError instanceof Error ? emailError.stack : undefined,
              orderId: order.id,
              customerEmail: order.email
            });
            // Don't fail the webhook for email errors
          }
        }
        break
      }

      case 'payment_intent.payment_failed': {
        const paymentIntent = event.data.object as Stripe.PaymentIntent
        
        // Update order status
        await supabase
          .from('orders')
          .update({
            status: 'cancelled',
            payment_status: 'failed'
          })
          .eq('payment_intent_id', paymentIntent.id)
        break
      }

      case 'payment_intent.requires_action': {
        const paymentIntent = event.data.object as Stripe.PaymentIntent
        
        // Update order status to pending action
        await supabase
          .from('orders')
          .update({
            payment_status: 'requires_action'
          })
          .eq('payment_intent_id', paymentIntent.id)
        break
      }

      default:
        console.log(`Unhandled event type: ${event.type}`)
    }

    return NextResponse.json({ received: true })

  } catch (error) {
    console.error('Webhook error:', error)
    return NextResponse.json(
      { error: 'Webhook handler failed' },
      { status: 500 }
    )
  }
}
