import { NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase/admin'
import { sendOrderConfirmationEmail, sendShippingNotificationEmail } from '@/lib/email'

export async function POST() {
  console.log('🧪 User Language Email Test: Starting test...');

  try {
    // Check if this is a development environment
    if (process.env.NODE_ENV !== 'development') {
      console.log('🧪 User Language Email Test: Only available in development');
      return NextResponse.json(
        { error: 'Test endpoint only available in development environment' },
        { status: 403 }
      );
    }

    // Get a test user for language testing (use environment variable or first admin user)
    const testEmail = process.env.TEST_USER_EMAIL || '<EMAIL>';
    const { data: userData, error: userError } = await supabaseAdmin
      .from('users')
      .select('id, email, first_name, last_name, preferred_language')
      .eq('email', testEmail)
      .single();

    if (userError || !userData) {
      console.error('🧪 User Language Email Test: Error fetching user:', userError);
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    console.log('🧪 User Language Email Test: User data:', userData);

    // Get a recent order for this user
    const { data: orderData, error: orderError } = await supabaseAdmin
      .from('orders')
      .select(`
        *,
        order_items (
          *,
          products (
            title
          )
        )
      `)
      .eq('user_id', userData.id)
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (orderError || !orderData) {
      console.error('🧪 User Language Email Test: Error fetching order:', orderError);
      return NextResponse.json(
        { error: 'No orders found for user' },
        { status: 404 }
      );
    }

    console.log('🧪 User Language Email Test: Order data:', {
      id: orderData.id,
      order_number: orderData.order_number,
      email: orderData.email,
      user_id: orderData.user_id
    });

    const results: {
      errors: string[];
      userLanguage: string;
      orderConfirmationEmail?: {
        success: boolean;
        messageId?: string;
        error?: string;
      };
      shippingNotificationEmail?: {
        success: boolean;
        messageId?: string;
        error?: string;
      };
    } = {
      errors: [],
      userLanguage: userData.preferred_language || 'de'
    };

    // Test order confirmation email with user's preferred language
    try {
      console.log('🧪 User Language Email Test: Testing order confirmation email with language:', userData.preferred_language);
      const confirmationResult = await sendOrderConfirmationEmail(orderData, userData.preferred_language || 'de');
      results.orderConfirmationEmail = {
        success: true,
        messageId: confirmationResult.messageId
      };
      console.log('🧪 User Language Email Test: Order confirmation email sent successfully');
    } catch (error) {
      console.error('🧪 User Language Email Test: Order confirmation email failed:', error);
      results.orderConfirmationEmail = {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
      results.errors.push('Order confirmation email failed: ' + (error instanceof Error ? error.message : String(error)));
    }

    // Test shipping notification email with user's preferred language
    try {
      console.log('🧪 User Language Email Test: Testing shipping notification email with language:', userData.preferred_language);
      const shippingResult = await sendShippingNotificationEmail(
        orderData, 
        'TEST123456789', 
        userData.preferred_language || 'de'
      );
      results.shippingNotificationEmail = {
        success: true,
        messageId: shippingResult.messageId
      };
      console.log('🧪 User Language Email Test: Shipping notification email sent successfully');
    } catch (error) {
      console.error('🧪 User Language Email Test: Shipping notification email failed:', error);
      results.shippingNotificationEmail = {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
      results.errors.push('Shipping notification email failed: ' + (error instanceof Error ? error.message : String(error)));
    }

    console.log('🧪 User Language Email Test: Test completed');
    console.log('🧪 User Language Email Test: Results:', results);

    return NextResponse.json({
      success: results.errors.length === 0,
      message: results.errors.length === 0 
        ? 'All email tests passed successfully' 
        : `${results.errors.length} email test(s) failed`,
      results,
      testDetails: {
        userEmail: userData.email,
        userLanguage: userData.preferred_language,
        orderNumber: orderData.order_number,
        companyName: 'BL Glamour Sagl (should appear in emails)'
      }
    });

  } catch (error) {
    console.error('🧪 User Language Email Test: Unexpected error:', error);
    return NextResponse.json(
      { 
        error: 'Test failed with unexpected error',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
