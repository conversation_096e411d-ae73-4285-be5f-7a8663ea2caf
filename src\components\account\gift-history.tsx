'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Gift, Star, Trophy, Package, CheckCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { formatCurrency } from '@/lib/utils';

interface Product {
  id: string;
  title: string;
  images: string[];
  price: number;
}

interface GiftReward {
  id: string;
  type: 'level' | 'league';
  trigger_value: number;
  option_1_product_id: string | null;
  option_2_product_id: string | null;
  option_3_product_id: string | null;
  option_1_product?: Product;
  option_2_product?: Product;
  option_3_product?: Product;
}

interface PendingGift {
  id: string;
  claimed_at: string;
  status: 'pending' | 'ready_for_order' | 'added_to_order' | 'fulfilled';
  selected_product_id?: string;
  selected_product?: Product;
  gift_rewards: GiftReward;
}

export default function GiftHistory() {
  const [pendingGifts, setPendingGifts] = useState<PendingGift[]>([]);
  const [loading, setLoading] = useState(true);
  const [selecting, setSelecting] = useState<string | null>(null);
  const t = useTranslations('account');
  const { toast } = useToast();

  useEffect(() => {
    fetchPendingGifts();
  }, []);

  const fetchPendingGifts = async () => {
    try {
      const response = await fetch('/api/account/gift-claims');
      if (response.ok) {
        const data = await response.json();
        setPendingGifts(data.claims || []);
      }
    } catch (error) {
      console.error('Error fetching pending gifts:', error);
    } finally {
      setLoading(false);
    }
  };

  const selectGift = async (giftId: string, productId: string) => {
    setSelecting(giftId);
    try {
      const response = await fetch('/api/gifts/select', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ claimId: giftId, productId })
      });

      if (response.ok) {
        toast({ title: 'Regalo selezionato!', description: 'Il tuo regalo è stato aggiunto al prossimo ordine.' });
        fetchPendingGifts(); // Refresh the list
      } else {
        throw new Error('Failed to select gift');
      }
    } catch (error) {
      console.error('Error selecting gift:', error);
      toast({ title: 'Errore', description: 'Impossibile selezionare il regalo.', variant: 'destructive' });
    } finally {
      setSelecting(null);
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Gift className="h-5 w-5" />
            {t('gifts.title')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Separate pending and ready gifts
  const pendingGiftsToSelect = pendingGifts.filter(gift => gift.status === 'pending');
  const readyGifts = pendingGifts.filter(gift => gift.status === 'ready_for_order');

  if (pendingGifts.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Gift className="h-5 w-5" />
            {t('gifts.title')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Gift className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">
              Nessun regalo da riscattare al momento.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Gift className="h-5 w-5" />
          {t('gifts.title')}
        </CardTitle>
        <p className="text-muted-foreground">
          {pendingGiftsToSelect.length > 0 && "Scegli il tuo regalo tra le opzioni disponibili!"}
          {readyGifts.length > 0 && pendingGiftsToSelect.length === 0 && "I tuoi regali selezionati verranno aggiunti automaticamente al prossimo ordine."}
          {pendingGiftsToSelect.length > 0 && readyGifts.length > 0 && "Scegli i tuoi regali e visualizza quelli già selezionati."}
        </p>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Ready gifts section */}
          {readyGifts.length > 0 && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-green-700 flex items-center gap-2">
                <CheckCircle className="h-5 w-5" />
                Regali aggiunti al prossimo ordine
              </h3>
              {readyGifts.map((gift) => {
                const selectedProduct = gift.selected_product ||
                  (gift.selected_product_id
                    ? [gift.gift_rewards.option_1_product, gift.gift_rewards.option_2_product, gift.gift_rewards.option_3_product]
                        .find(p => p?.id === gift.selected_product_id)
                    : null);

                return (
                  <Card key={gift.id} className="border-l-4 border-l-green-500 bg-green-50">
                    <CardContent className="pt-6">
                      <div className="flex items-center gap-3 mb-4">
                        <div className="bg-green-100 p-2 rounded-full">
                          {gift.gift_rewards.type === 'league' ? (
                            <Trophy className="h-5 w-5 text-green-600" />
                          ) : (
                            <Star className="h-5 w-5 text-green-600" />
                          )}
                        </div>
                        <div>
                          <h4 className="font-semibold text-green-800">
                            Regalo {gift.gift_rewards.type === 'league' ? 'Lega' : 'Livello'} {gift.gift_rewards.trigger_value}
                          </h4>
                          <p className="text-sm text-green-600">Aggiunto al prossimo ordine</p>
                        </div>
                      </div>

                      {selectedProduct && (
                        <div className="flex items-center gap-4 p-4 bg-white rounded-lg border">
                          {selectedProduct.images?.[0] && (
                            <Image
                              src={selectedProduct.images[0]}
                              alt={selectedProduct.title}
                              width={64}
                              height={64}
                              className="w-16 h-16 object-cover rounded-lg"
                            />
                          )}
                          <div className="flex-1">
                            <h5 className="font-medium">{selectedProduct.title}</h5>
                            <p className="text-sm text-muted-foreground">
                              Valore: {formatCurrency(selectedProduct.price)}
                            </p>
                          </div>
                          <CheckCircle className="h-6 w-6 text-green-500" />
                        </div>
                      )}
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          )}

          {/* Pending gifts section */}
          {pendingGiftsToSelect.map((gift) => (
            <Card key={gift.id} className="border-l-4 border-l-green-500">
              <CardContent className="pt-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className="bg-green-100 p-2 rounded-full">
                    {gift.gift_rewards.type === 'league' ? (
                      <Trophy className="h-5 w-5 text-green-600" />
                    ) : (
                      <Star className="h-5 w-5 text-green-600" />
                    )}
                  </div>
                  <div>
                    <h4 className="font-semibold">
                      {gift.gift_rewards.type === 'league'
                        ? `🏆 Regalo Lega ${gift.gift_rewards.trigger_value}`
                        : `⭐ Regalo Livello ${gift.gift_rewards.trigger_value}`
                      }
                    </h4>
                    <Badge variant="secondary" className="bg-green-50 text-green-700">
                      Da riscattare
                    </Badge>
                  </div>
                </div>

                <div className="space-y-3">
                  <p className="text-sm font-medium flex items-center gap-2">
                    <Package className="h-4 w-4" />
                    Scegli il tuo regalo:
                  </p>

                  <div className="grid gap-3 md:grid-cols-3">
                    {[
                      { product: gift.gift_rewards.option_1_product, id: gift.gift_rewards.option_1_product_id },
                      { product: gift.gift_rewards.option_2_product, id: gift.gift_rewards.option_2_product_id },
                      { product: gift.gift_rewards.option_3_product, id: gift.gift_rewards.option_3_product_id }
                    ].filter(option => option.product && option.id).map((option, index) => (
                      <div key={index} className="border rounded-lg p-3 hover:bg-gray-50 transition-colors">
                        <div className="flex items-center gap-3 mb-3">
                          {option.product!.images?.[0] && (
                            <Image
                              src={option.product!.images[0]}
                              alt={option.product!.title}
                              width={48}
                              height={48}
                              className="rounded object-cover"
                            />
                          )}
                          <div className="flex-1">
                            <p className="font-medium text-sm">{option.product!.title}</p>
                            <p className="text-xs text-muted-foreground">
                              CHF {option.product!.price.toFixed(2)}
                            </p>
                          </div>
                        </div>
                        <Button
                          size="sm"
                          className="w-full"
                          onClick={() => selectGift(gift.id, option.id!)}
                          disabled={selecting === gift.id}
                        >
                          {selecting === gift.id ? 'Selezionando...' : 'Scegli questo'}
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
