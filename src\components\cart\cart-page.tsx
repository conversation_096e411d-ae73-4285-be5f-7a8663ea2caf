'use client';

import { useTranslations } from 'next-intl';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { formatCurrency } from '@/lib/utils';
import Image from 'next/image';
import Link from 'next/link';
import { ShoppingCart, ArrowLeft, Truck, Loader2 } from 'lucide-react';
import { CartItemActions } from '@/components/cart/cart-item-actions';
import { useCart } from '@/lib/cart';

export function CartPage({ locale }: { locale: string }) {
  const t = useTranslations('cart');
  const { cart, loading } = useCart();
  
  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto text-center">
          <Loader2 className="mx-auto h-8 w-8 animate-spin mb-4" />
          <p>{t('loading')}</p>
        </div>
      </div>
    );
  }
  
  if (!cart || !cart.items || cart.items.length === 0) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto text-center">
          <div className="mb-8">
            <ShoppingCart className="mx-auto h-24 w-24 text-muted-foreground mb-4" />
            <h1 className="text-3xl font-bold mb-4">{t('empty')}</h1>
            <p className="text-muted-foreground mb-8">
              {t('emptyDescription')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" asChild>
                <Link href={`/${locale}/coffee-box-builder`}>
                  {t('coffeeBoxBuilder')}
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href={`/${locale}/shop`}>
                  {t('continueShopping')}
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Calculate totals - prices are VAT-inclusive
  const subtotal = cart.items.reduce((sum, item) => {
    let price = 0
    if (item.product) {
      price = item.product.discount_price || item.product.price || 0
    } else if (item.bundle) {
      price = item.bundle.discount_price || item.bundle.total_price || 0
    }
    return sum + (price * item.quantity)
  }, 0)

  const freeShippingThreshold = 90
  const shippingCost = subtotal >= freeShippingThreshold ? 0 : 8.90
  const total = subtotal + shippingCost // Total is VAT-inclusive

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <Button variant="ghost" asChild className="mb-4">
          <Link href={`/${locale}/shop`}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('continueShopping')}
          </Link>
        </Button>
        <h1 className="text-3xl font-bold">{t('title')} ({cart.items.length})</h1>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2 space-y-4">
          {cart.items.map((item) => (
            <Card key={item.id} className="p-4">
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="relative w-full sm:w-32 h-32 bg-muted rounded-md overflow-hidden">
                  {(item.product?.images?.[0] || item.bundle?.image) ? (
                    <Image
                      src={item.product?.images?.[0] || item.bundle?.image || ''}
                      alt={item.product?.title || item.bundle?.title || ''}
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center bg-muted">
                      <ShoppingCart className="h-8 w-8 text-muted-foreground" />
                    </div>
                  )}
                </div>
                <div className="flex-1">
                  <div className="flex justify-between">
                    <div>
                      <h3 className="font-medium">{item.product?.title || item.bundle?.title}</h3>
                      {item.product?.brand && (
                        <p className="text-sm text-muted-foreground">{item.product.brand}</p>
                      )}
                      {item.product?.type && (
                        <p className="text-sm text-muted-foreground">
                          {t(`coffeeTypes.${item.product.type}`, { defaultValue: item.product.type })}
                        </p>
                      )}
                      {item.bundle && (
                        <p className="text-sm text-muted-foreground">{t('bundle')}</p>
                      )}
                    </div>
                    <div className="text-right">
                      <p className="font-medium">
                        {formatCurrency((() => {
                          let price = 0
                          if (item.product) {
                            price = item.product.discount_price || item.product.price || 0
                          } else if (item.bundle) {
                            price = item.bundle.discount_price || item.bundle.total_price || 0
                          }
                          return price * item.quantity
                        })())}
                      </p>
                      {((item.product?.discount_price && item.product.discount_price > 0 && item.product.discount_price < item.product.price) ||
                        (item.bundle?.discount_price && item.bundle.discount_price > 0 && item.bundle.discount_price < item.bundle.total_price)) && (
                        <p className="text-sm text-muted-foreground line-through">
                          {formatCurrency((() => {
                            let originalPrice = 0
                            if (item.product?.discount_price && item.product.discount_price > 0 && item.product.discount_price < item.product.price) {
                              originalPrice = item.product.price || 0
                            } else if (item.bundle?.discount_price && item.bundle.discount_price > 0 && item.bundle.discount_price < item.bundle.total_price) {
                              originalPrice = item.bundle.total_price || 0
                            }
                            return originalPrice * item.quantity
                          })())}
                        </p>
                      )}
                    </div>
                  </div>
                  <div className="mt-4 flex items-center justify-between">
                    <CartItemActions
                      itemId={item.id}
                      currentQuantity={item.quantity}
                    />
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>

        <div className="space-y-4">
          <Card>
            <div className="p-6 space-y-4">
              <h2 className="text-lg font-medium">{t('orderSummary')}</h2>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">{t('subtotal')}</span>
                  <span>{formatCurrency(subtotal)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">{t('shipping')}</span>
                  <span>
                    {shippingCost === 0 ? (
                      <span className="text-green-600">{t('freeShipping')}</span>
                    ) : (
                      formatCurrency(shippingCost)
                    )}
                  </span>
                </div>
                {shippingCost > 0 && subtotal < freeShippingThreshold && (
                  <div className="text-sm text-muted-foreground">
                    {t('freeShippingAt', { amount: formatCurrency(freeShippingThreshold - subtotal) })}
                  </div>
                )}
                <div className="border-t pt-4 mt-4 flex justify-between font-medium">
                  <span>{t('total')}</span>
                  <span>{formatCurrency(total)}</span>
                </div>
              </div>
              <Button className="w-full" size="lg" asChild>
                <Link href={`/${locale}/checkout`}>
                  {t('proceedToCheckout')}
                </Link>
              </Button>
              {shippingCost > 0 && (
                <div className="flex items-center text-sm text-muted-foreground">
                  <Truck className="mr-2 h-4 w-4" />
                  <span>{t('shippingInfo')}</span>
                </div>
              )}
            </div>
          </Card>
        </div>
      </div>
    </div>
  )
}
