import { NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { getUserLevel, calculateLeague, getLeagueDiscount, getPointsPerCHF } from '@/lib/gamification';

export async function GET() {
  try {
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { data: profile, error: profileError } = await supabase
      .from('users')
      .select('total_points, lifetime_spend, current_level')
      .eq('id', user.id)
      .single();
    if (profileError || !profile) {
      return NextResponse.json({ error: 'Profile not found' }, { status: 404 });
    }

    // Get user level information based on total points
    const currentLevel = await getUserLevel(profile.total_points || 0);

    // Get next level information
    const { data: nextLevel } = await supabase
      .from('user_levels')
      .select('*')
      .gt('minimum_points', profile.total_points || 0)
      .order('minimum_points', { ascending: true })
      .limit(1)
      .single();

    // Calculate points needed for next level
    const pointsToNext = nextLevel ? nextLevel.minimum_points - (profile.total_points || 0) : 0;

    // Get next gift threshold based on points (simplified)
    const { data: nextGift } = await supabase
      .from('gift_thresholds')
      .select('*')
      .eq('is_active', true)
      .gt('threshold_points', profile.total_points || 0)
      .order('threshold_points', { ascending: true })
      .limit(1)
      .single();

    // Calculate points needed for next gift
    const pointsToNextGift = nextGift
      ? nextGift.threshold_points - (profile.total_points || 0)
      : 0;

    // Calculate league information - always use dynamically calculated level
    const userLevel = currentLevel?.level || 1;
    const league = calculateLeague(userLevel);
    const leagueDiscountPercent = await getLeagueDiscount(league);

    // Get league name
    const getLeagueName = (leagueNum: number) => {
      switch (leagueNum) {
        case 1: return 'Bronze League';
        case 2: return 'Silver League';
        case 3: return 'Gold League';
        case 4: return 'Platinum League';
        case 5: return 'Diamond League';
        default: return 'Diamond League';
      }
    };

    // Get points per CHF ratio for calculating equivalent spend
    const pointsPerCHF = await getPointsPerCHF();

    // Calculate total savings from discounts
    const { data: orders } = await supabase
      .from('orders')
      .select('discount_amount')
      .eq('user_id', user.id)
      .eq('payment_status', 'paid');

    const totalSavings = orders?.reduce((sum, order) => sum + (order.discount_amount || 0), 0) || 0;

    // Calculate total value of gifts received using the new gift system
    const { data: userGiftClaims } = await supabase
      .from('user_gift_claims')
      .select(`
        selected_product_id,
        status,
        gift_rewards (
          option_1_product_id,
          option_2_product_id,
          option_3_product_id
        )
      `)
      .eq('user_id', user.id)
      .in('status', ['selected', 'fulfilled']);

    let totalGiftValue = 0;
    if (userGiftClaims) {
      // Get all product IDs from selected gifts
      const productIds = userGiftClaims
        .filter(claim => claim.selected_product_id)
        .map(claim => claim.selected_product_id);

      if (productIds.length > 0) {
        const { data: products } = await supabase
          .from('products')
          .select('id, price')
          .in('id', productIds);

        if (products) {
          totalGiftValue = products.reduce((sum, product) => sum + (product.price || 0), 0);
        }
      }
    }

    return NextResponse.json({
      currentLevel: userLevel,
      currentLevelName: currentLevel?.name || 'Bronze I',
      totalPoints: profile.total_points || 0,
      lifetimeSpend: profile.lifetime_spend || 0,
      pointsToNext,
      nextLevelName: nextLevel?.name || null,
      discountPercent: leagueDiscountPercent, // Now uses league-based discount
      pointsMultiplier: 1, // Always 1 in league system
      pointsToNextGift,
      nextGiftThreshold: nextGift?.threshold_points || null,
      pointsPerCHF: pointsPerCHF,
      // New league information
      league: league,
      leagueName: getLeagueName(league),
      leagueDiscountPercent: leagueDiscountPercent,
      // New statistics
      totalSavings: totalSavings,
      totalGiftValue: totalGiftValue
    });
  } catch (error) {
    console.error('Error in loyalty API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
