'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useTranslations, useLocale } from 'next-intl'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { createClient } from '@/lib/supabase/client'
import { getAuthErrorKey } from '@/lib/auth-errors'
import { Eye, EyeOff, Coffee } from 'lucide-react'

export default function LoginPage() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const router = useRouter()
  const t = useTranslations('auth.login')
  const locale = useLocale()

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')

    try {
      const supabase = createClient()
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) {
        const errorKey = getAuthErrorKey(error.message, 'login')
        setError(t(errorKey))
      } else if (data.user) {
        // Link any existing guest orders to this user account
        console.log('🛒 Login: Checking for guest orders to link...')
        try {
          const { data: linkedOrders, error: linkError } = await supabase
            .from('orders')
            .update({ user_id: data.user.id })
            .eq('email', email)
            .is('user_id', null)
            .select('id, order_number')

          if (linkError) {
            console.error('🛒 Login: Error linking guest orders:', linkError)
            // Don't fail login for order linking issues
          } else {
            const orderCount = linkedOrders?.length || 0
            console.log(`🛒 Login: Successfully linked ${orderCount} guest orders to user account`)
            if (orderCount > 0) {
              console.log('🛒 Login: Linked orders:', linkedOrders?.map(o => o.order_number).join(', '))

              // Award points retroactively for linked orders
              try {
                const linkedOrderIds = linkedOrders.map(o => o.id)
                const response = await fetch('/api/gamification/award-retroactive-points', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  body: JSON.stringify({ linkedOrderIds })
                })

                if (response.ok) {
                  const { totalPointsAwarded, ordersProcessed } = await response.json()
                  if (totalPointsAwarded > 0) {
                    console.log(`🎮 Login: Awarded ${totalPointsAwarded} points for ${ordersProcessed} linked orders`)
                  }
                }
              } catch (pointsError) {
                console.error('🎮 Login: Error awarding retroactive points:', pointsError)
                // Don't fail login for points errors
              }
            }
          }
        } catch (linkingError) {
          console.error('🛒 Login: Exception in order linking process:', linkingError)
          // Don't fail login for order linking issues
        }

        const { data: profile } = await supabase
          .from('users')
          .select('is_admin')
          .eq('id', data.user.id)
          .single()

        const redirectPath = profile?.is_admin
          ? `/${locale}/admin`
          : `/${locale}`
        router.push(redirectPath)
        router.refresh()
      }
    } catch {
      setError(t('errors.generic'))
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card>
      <CardHeader className="text-center">
        <div className="flex justify-center mb-4">
          <Coffee className="h-12 w-12 text-primary" />
        </div>
        <CardTitle className="text-2xl">{t('title')}</CardTitle>
        <CardDescription>
          {t('subtitle')}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleLogin} className="space-y-4">
          <div className="space-y-2">
            <label htmlFor="email" className="text-sm font-medium">
              {t('email')}
            </label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder={t('emailPlaceholder')}
              required
              disabled={isLoading}
            />
          </div>
          
          <div className="space-y-2">
            <label htmlFor="password" className="text-sm font-medium">
              {t('password')}
            </label>
            <div className="relative">
              <Input
                id="password"
                type={showPassword ? 'text' : 'password'}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder={t('password')}
                required
                disabled={isLoading}
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowPassword(!showPassword)}
                disabled={isLoading}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>

          {error && (
            <div className="text-sm text-red-600 bg-red-50 p-3 rounded-md">
              {error}
            </div>
          )}

          <Button type="submit" className="w-full" disabled={isLoading}>
            {isLoading ? '...' : t('submit')}
          </Button>
        </form>

        <div className="mt-6 text-center text-sm">
          <span className="text-muted-foreground">{t('noAccount')} </span>
          <Link href={`/${locale}/register`} className="text-primary hover:underline">
            {t('signUp')}
          </Link>
        </div>

        <div className="mt-4 text-center">
          <Link
            href={`/${locale}/forgot-password`}
            className="text-sm text-muted-foreground hover:text-primary hover:underline"
          >
            {t('forgotPassword')}
          </Link>
        </div>
      </CardContent>
    </Card>
  )
}
