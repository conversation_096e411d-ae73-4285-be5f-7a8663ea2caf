// Database Types
export interface User {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  phone?: string;
  date_of_birth?: string;
  created_at: string;
  updated_at: string;
  lifetime_spend: number;
  current_level: number;
  total_points: number;
  is_admin: boolean;
}

export interface UserAddress {
  id: string;
  user_id: string;
  type: 'billing' | 'shipping';
  first_name: string;
  last_name: string;
  company?: string;
  street_address: string;
  city: string;
  postal_code: string;
  country: string;
  is_default: boolean;
  created_at: string;
  updated_at: string;
}

export interface Product {
  id: string;
  title: string;
  description: string;
  slug: string;
  category: 'coffee' | 'accessories';
  coffee_type?: 'capsules' | 'pods' | 'beans' | 'ground';
  brand?: string;
  blend?: string;
  machine_compatibility?: string[];
  pack_quantity?: number;
  pack_weight_grams?: number;
  price: number;
  discount_price?: number;
  cost_per_espresso?: number;
  images: string[];
  inventory_count: number;
  purchase_cost?: number;
  is_available: boolean;
  created_at: string;
  updated_at: string;
}

export interface Bundle {
  id: string;
  title: string;
  description: string;
  slug: string;
  image: string;
  total_price: number;
  discount_price?: number;
  is_available: boolean;
  created_at: string;
  updated_at: string;
}

export interface BundleItem {
  id: string;
  bundle_id: string;
  product_id: string;
  quantity: number;
  product?: Product;
}

export interface CartItem {
  id: string;
  user_id?: string;
  session_id?: string;
  product_id: string;
  quantity: number;
  created_at: string;
  updated_at: string;
  product?: Product;
}

export interface Order {
  id: string;
  order_number?: string;
  user_id?: string;
  email: string;
  status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  subtotal: number;
  shipping_cost: number;
  tax_amount: number;
  discount_amount: number;
  total_amount: number;
  currency: string;
  payment_status: 'pending' | 'paid' | 'failed' | 'refunded';
  payment_intent_id?: string;
  shipping_address: UserAddress;
  billing_address: UserAddress;
  tracking_number?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface OrderItem {
  id: string;
  order_id: string;
  product_id: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  is_gift?: boolean;
  gift_claim_id?: string;
  product?: Product;
}

export interface Coupon {
  id: string;
  code: string;
  type: 'percentage' | 'fixed_amount';
  value: number;
  minimum_order_amount?: number;
  usage_limit?: number;
  used_count: number;
  valid_from: string;
  valid_until: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface UserLevel {
  id: string;
  level: number;
  name: string;
  minimum_spend: number;
  discount_percentage: number;
  points_multiplier: number;
  created_at: string;
  updated_at: string;
}

export interface GiftThreshold {
  id: string;
  threshold_points: number;
  gift_product_ids: string[];
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface ShippingRate {
  id: string;
  country: string;
  cost: number;
  free_shipping_threshold?: number;
  estimated_days: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Form Types
export interface CheckoutFormData {
  email: string;
  shipping_address: Omit<UserAddress, 'id' | 'user_id' | 'created_at' | 'updated_at'>;
  billing_address: Omit<UserAddress, 'id' | 'user_id' | 'created_at' | 'updated_at'>;
  billing_same_as_shipping: boolean;
  create_account: boolean;
  password?: string;
  marketing_consent: boolean;
}

export interface ProductFormData {
  title: string;
  description: string;
  category: 'coffee' | 'accessories';
  coffee_type?: 'capsules' | 'pods' | 'beans' | 'ground';
  brand?: string;
  blend?: string;
  machine_compatibility?: string[];
  pack_quantity?: number;
  pack_weight_grams?: number;
  price: number;
  discount_price?: number;
  inventory_count: number;
  purchase_cost?: number;
  is_available: boolean;
}

// API Response Types
export interface ApiResponse<T> {
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Coffee Box Builder Types
export interface CoffeeBoxItem {
  product_id: string;
  quantity: number;
  product?: Product;
}

export interface CoffeeBox {
  items: CoffeeBoxItem[];
  total_price: number;
  estimated_cost_per_espresso: number;
}

// Analytics Types
export interface AnalyticsData {
  revenue: {
    gross: number;
    net: number;
    monthly: Array<{ month: string; amount: number }>;
  };
  orders: {
    total: number;
    monthly: Array<{ month: string; count: number }>;
  };
  customers: {
    total: number;
    new: number;
    returning: number;
  };
  aov: number;
  cart_abandonment_rate: number;
}
