'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'

interface GiftProduct {
  id: string
  title: string
  images: string[]
  price: number
}

interface GiftReadyForOrder {
  id: string
  selected_product_id: string
  claimed_at: string
  gift_rewards: {
    type: 'level' | 'league'
    trigger_value: number
  }[]
  products: GiftProduct[]
}

export function useGiftsReadyForOrder(userId?: string) {
  const [gifts, setGifts] = useState<GiftReadyForOrder[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (!userId) {
      setGifts([])
      setLoading(false)
      return
    }

    const fetchGifts = async () => {
      try {
        const supabase = createClient()
        const { data, error } = await supabase
          .from('user_gift_claims')
          .select(`
            id,
            selected_product_id,
            claimed_at,
            gift_rewards!inner(type, trigger_value),
            products:selected_product_id(id, title, images, price)
          `)
          .eq('user_id', userId)
          .eq('status', 'ready_for_order')
          .order('claimed_at', { ascending: false })

        if (error) {
          console.error('Error fetching gifts ready for order:', error)
          setGifts([])
        } else {
          setGifts((data as GiftReadyForOrder[]) || [])
        }
      } catch (error) {
        console.error('Error fetching gifts ready for order:', error)
        setGifts([])
      } finally {
        setLoading(false)
      }
    }

    fetchGifts()
  }, [userId])

  return { gifts, loading }
}
